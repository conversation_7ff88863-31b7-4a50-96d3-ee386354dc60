const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const { exec } = require('child_process');

let mainWindow;

function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1000,
        height: 800,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, 'preload.js')
        },
        icon: path.join(__dirname, 'assets', 'icon.png'), // 可选：添加应用图标
        title: '自动关机应用'
    });

    mainWindow.loadFile('index.html');

    // 开发环境下打开开发者工具
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }
}

// 应用准备就绪时创建窗口
app.whenReady().then(createWindow);

// 所有窗口关闭时退出应用（macOS除外）
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// macOS上点击dock图标时重新创建窗口
app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// IPC 处理程序

// 保存配置文件
ipcMain.handle('save-config', async (event, config) => {
    try {
        const configPath = path.join(__dirname, 'shutdown-settings.json');
        await fs.writeFile(configPath, JSON.stringify(config, null, 2), 'utf8');
        return { success: true };
    } catch (error) {
        console.error('保存配置失败:', error);
        throw error;
    }
});

// 加载配置文件
ipcMain.handle('load-config', async () => {
    try {
        const configPath = path.join(__dirname, 'shutdown-settings.json');
        const data = await fs.readFile(configPath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('加载配置失败:', error);
        // 如果文件不存在，返回默认配置
        return [{ hh: 0, mm: 0, ss: 0, days: [] }];
    }
});

// 执行关机命令
ipcMain.handle('shutdown', async () => {
    try {
        let command;

        // 根据操作系统选择关机命令
        switch (process.platform) {
            case 'win32':
                command = 'shutdown /s /t 0';
                break;
            case 'darwin':
                command = 'sudo shutdown -h now';
                break;
            case 'linux':
                command = 'shutdown -h now';
                break;
            default:
                throw new Error('不支持的操作系统');
        }

        return new Promise((resolve, reject) => {
            exec(command, (error, stdout, stderr) => {
                if (error) {
                    console.error('关机命令执行失败:', error);
                    reject(error);
                } else {
                    console.log('关机命令已执行');
                    resolve({ success: true });
                }
            });
        });
    } catch (error) {
        console.error('关机失败:', error);
        throw error;
    }
});

// 获取系统信息
ipcMain.handle('get-system-info', () => {
    return {
        platform: process.platform,
        arch: process.arch,
        version: process.version
    };
});