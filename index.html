<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动关机应用</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .time-input {
            font-size: 1.2rem;
            text-align: center;
            font-weight: bold;
        }
        .day-checkbox {
            transform: scale(1.2);
        }
        .status-card {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <!-- 标题 -->
                <div class="text-center mb-4">
                    <h1 class="text-white mb-3">
                        <i class="bi bi-power"></i> 自动关机应用
                    </h1>
                    <p class="text-white-50">设置您的自动关机时间和日期</p>
                </div>

                <!-- 主设置卡片 -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-gear-fill"></i> 关机设置</h5>
                    </div>
                    <div class="card-body">
                        <!-- 时间设置 -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <label class="form-label fw-bold">关机时间</label>
                                <div class="row g-2">
                                    <div class="col-4">
                                        <label class="form-label small">小时</label>
                                        <input type="number" class="form-control time-input" id="hours" 
                                               min="0" max="23" value="0">
                                    </div>
                                    <div class="col-4">
                                        <label class="form-label small">分钟</label>
                                        <input type="number" class="form-control time-input" id="minutes" 
                                               min="0" max="59" value="0">
                                    </div>
                                    <div class="col-4">
                                        <label class="form-label small">秒</label>
                                        <input type="number" class="form-control time-input" id="seconds" 
                                               min="0" max="59" value="0">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 星期设置 -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">执行日期</label>
                            <div class="row g-2">
                                <div class="col-6 col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input day-checkbox" type="checkbox" 
                                               id="day0" value="0">
                                        <label class="form-check-label" for="day0">周日</label>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input day-checkbox" type="checkbox" 
                                               id="day1" value="1">
                                        <label class="form-check-label" for="day1">周一</label>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input day-checkbox" type="checkbox" 
                                               id="day2" value="2">
                                        <label class="form-check-label" for="day2">周二</label>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input day-checkbox" type="checkbox" 
                                               id="day3" value="3">
                                        <label class="form-check-label" for="day3">周三</label>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input day-checkbox" type="checkbox" 
                                               id="day4" value="4">
                                        <label class="form-check-label" for="day4">周四</label>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input day-checkbox" type="checkbox" 
                                               id="day5" value="5">
                                        <label class="form-check-label" for="day5">周五</label>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input day-checkbox" type="checkbox" 
                                               id="day6" value="6">
                                        <label class="form-check-label" for="day6">周六</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-secondary me-md-2" id="loadBtn">
                                <i class="bi bi-arrow-clockwise"></i> 加载配置
                            </button>
                            <button type="button" class="btn btn-success" id="saveBtn">
                                <i class="bi bi-check-circle"></i> 保存设置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 状态显示卡片 -->
                <div class="card status-card">
                    <div class="card-body text-center">
                        <h5 class="card-title">
                            <i class="bi bi-info-circle"></i> 当前状态
                        </h5>
                        <p class="card-text" id="statusText">请设置关机时间和日期</p>
                        <div class="mt-3">
                            <button type="button" class="btn btn-light me-2" id="startBtn">
                                <i class="bi bi-play-fill"></i> 启动监控
                            </button>
                            <button type="button" class="btn btn-outline-light" id="stopBtn" disabled>
                                <i class="bi bi-stop-fill"></i> 停止监控
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 应用逻辑 -->
    <script>
        class ShutdownApp {
            constructor() {
                this.configFile = 'shutdown-settings.json';
                this.isMonitoring = false;
                this.monitorInterval = null;
                this.init();
            }

            init() {
                this.bindEvents();
                this.loadConfig();
            }

            bindEvents() {
                // 保存按钮
                document.getElementById('saveBtn').addEventListener('click', () => {
                    this.saveConfig();
                });

                // 加载按钮
                document.getElementById('loadBtn').addEventListener('click', () => {
                    this.loadConfig();
                });

                // 启动监控按钮
                document.getElementById('startBtn').addEventListener('click', () => {
                    this.startMonitoring();
                });

                // 停止监控按钮
                document.getElementById('stopBtn').addEventListener('click', () => {
                    this.stopMonitoring();
                });

                // 实时保存配置（双向绑定）
                ['hours', 'minutes', 'seconds'].forEach(id => {
                    document.getElementById(id).addEventListener('input', () => {
                        this.autoSave();
                    });
                });

                // 星期复选框事件
                for (let i = 0; i <= 6; i++) {
                    document.getElementById(`day${i}`).addEventListener('change', () => {
                        this.autoSave();
                    });
                }
            }

            // 获取当前配置
            getCurrentConfig() {
                const hours = parseInt(document.getElementById('hours').value) || 0;
                const minutes = parseInt(document.getElementById('minutes').value) || 0;
                const seconds = parseInt(document.getElementById('seconds').value) || 0;
                
                const days = [];
                for (let i = 0; i <= 6; i++) {
                    if (document.getElementById(`day${i}`).checked) {
                        days.push(i);
                    }
                }

                return { hh: hours, mm: minutes, ss: seconds, days: days };
            }

            // 设置界面配置
            setUIConfig(config) {
                document.getElementById('hours').value = config.hh || 0;
                document.getElementById('minutes').value = config.mm || 0;
                document.getElementById('seconds').value = config.ss || 0;

                // 清除所有复选框
                for (let i = 0; i <= 6; i++) {
                    document.getElementById(`day${i}`).checked = false;
                }

                // 设置选中的日期
                if (config.days && Array.isArray(config.days)) {
                    config.days.forEach(day => {
                        const checkbox = document.getElementById(`day${day}`);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });
                }
            }

            // 保存配置
            async saveConfig() {
                try {
                    const config = this.getCurrentConfig();
                    const configArray = [config]; // 保持数组格式以兼容现有配置
                    
                    // 使用 Electron API 保存文件
                    if (window.electronAPI) {
                        await window.electronAPI.saveConfig(configArray);
                        this.showStatus('配置已保存', 'success');
                    } else {
                        // 浏览器环境下的模拟保存
                        localStorage.setItem('shutdownConfig', JSON.stringify(configArray));
                        this.showStatus('配置已保存到本地存储', 'success');
                    }
                } catch (error) {
                    console.error('保存配置失败:', error);
                    this.showStatus('保存配置失败', 'error');
                }
            }

            // 加载配置
            async loadConfig() {
                try {
                    let config;
                    
                    if (window.electronAPI) {
                        config = await window.electronAPI.loadConfig();
                    } else {
                        // 浏览器环境下从本地存储加载
                        const stored = localStorage.getItem('shutdownConfig');
                        config = stored ? JSON.parse(stored) : [{ hh: 0, mm: 0, ss: 0, days: [] }];
                    }

                    if (config && config.length > 0) {
                        this.setUIConfig(config[0]);
                        this.showStatus('配置已加载', 'success');
                    }
                } catch (error) {
                    console.error('加载配置失败:', error);
                    this.showStatus('加载配置失败', 'error');
                }
            }

            // 自动保存（双向绑定）
            autoSave() {
                // 延迟保存，避免频繁写入
                clearTimeout(this.autoSaveTimeout);
                this.autoSaveTimeout = setTimeout(() => {
                    this.saveConfig();
                }, 1000);
            }

            // 显示状态信息
            showStatus(message, type = 'info') {
                const statusText = document.getElementById('statusText');
                statusText.textContent = message;
                
                // 可以根据类型改变样式
                if (type === 'success') {
                    statusText.className = 'card-text text-success';
                } else if (type === 'error') {
                    statusText.className = 'card-text text-danger';
                } else {
                    statusText.className = 'card-text';
                }

                // 3秒后恢复默认状态
                setTimeout(() => {
                    statusText.className = 'card-text';
                    if (!this.isMonitoring) {
                        statusText.textContent = '请设置关机时间和日期';
                    }
                }, 3000);
            }

            // 启动监控
            startMonitoring() {
                const config = this.getCurrentConfig();
                
                if (config.days.length === 0) {
                    this.showStatus('请至少选择一个执行日期', 'error');
                    return;
                }

                this.isMonitoring = true;
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;

                this.showStatus(`监控已启动 - ${config.hh}:${config.mm}:${config.ss}`, 'success');

                // 每秒检查一次
                this.monitorInterval = setInterval(() => {
                    this.checkShutdownTime();
                }, 1000);
            }

            // 停止监控
            stopMonitoring() {
                this.isMonitoring = false;
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;

                if (this.monitorInterval) {
                    clearInterval(this.monitorInterval);
                    this.monitorInterval = null;
                }

                this.showStatus('监控已停止', 'info');
            }

            // 检查关机时间
            checkShutdownTime() {
                const now = new Date();
                const config = this.getCurrentConfig();

                const currentDay = now.getDay();
                const currentHour = now.getHours();
                const currentMinute = now.getMinutes();
                const currentSecond = now.getSeconds();

                // 检查是否在指定的日期
                if (config.days.includes(currentDay)) {
                    // 检查时间是否匹配
                    if (currentHour === config.hh && 
                        currentMinute === config.mm && 
                        currentSecond === config.ss) {
                        
                        this.executeShutdown();
                    }
                }

                // 更新状态显示
                const timeStr = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}:${currentSecond.toString().padStart(2, '0')}`;
                const targetStr = `${config.hh.toString().padStart(2, '0')}:${config.mm.toString().padStart(2, '0')}:${config.ss.toString().padStart(2, '0')}`;
                document.getElementById('statusText').textContent = `监控中... 当前: ${timeStr} | 目标: ${targetStr}`;
            }

            // 执行关机
            async executeShutdown() {
                this.showStatus('准备关机...', 'error');
                
                if (window.electronAPI) {
                    try {
                        await window.electronAPI.shutdown();
                    } catch (error) {
                        console.error('关机失败:', error);
                        this.showStatus('关机失败', 'error');
                    }
                } else {
                    // 浏览器环境下的模拟
                    alert('关机时间到！（浏览器环境下无法执行实际关机）');
                }

                this.stopMonitoring();
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new ShutdownApp();
        });
    </script>
</body>
</html>
