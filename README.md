# 自动关机应用

一个基于 Electron 的自动关机应用，支持设置特定时间和星期几执行关机操作。

## 功能特性

- 🕒 **精确时间设置**: 支持小时、分钟、秒的精确设置
- 📅 **星期选择**: 可选择一周中的任意几天执行关机
- 💾 **配置持久化**: 设置自动保存到 JSON 配置文件
- 🔄 **双向绑定**: 界面与配置文件实时同步
- 🎨 **美观界面**: 使用 Bootstrap 5 构建的现代化界面
- 🖥️ **跨平台**: 支持 Windows、macOS 和 Linux

## 安装和运行

### 前提条件
- Node.js (推荐 v16 或更高版本)
- npm

### 安装依赖
```bash
npm install
```

### 运行应用
```bash
npm run electron
```

## 使用说明

### 1. 设置关机时间
- 在"关机时间"区域设置小时(0-23)、分钟(0-59)、秒(0-59)
- 时间采用24小时制

### 2. 选择执行日期
- 勾选需要执行关机的星期几
- 可以选择多个日期
- 星期日=0, 星期一=1, ..., 星期六=6

### 3. 保存和加载配置
- 点击"保存设置"按钮保存当前配置
- 点击"加载配置"按钮从文件加载配置
- 配置会自动保存，支持双向绑定

### 4. 启动监控
- 点击"启动监控"开始监控系统时间
- 当到达设定时间且在指定日期时，系统将自动关机
- 点击"停止监控"可以停止监控

## 配置文件

配置保存在 `shutdown-settings.json` 文件中，格式如下：

```json
[
  {
    "hh": 22,
    "mm": 30,
    "ss": 0,
    "days": [1, 2, 3, 4, 5]
  }
]
```

- `hh`: 小时 (0-23)
- `mm`: 分钟 (0-59)
- `ss`: 秒 (0-59)
- `days`: 星期数组 (0=周日, 1=周一, ..., 6=周六)

## 安全说明

⚠️ **重要提醒**:
- 此应用会执行系统关机命令，请谨慎使用
- 建议在测试时先设置较短的时间间隔
- 关机前请保存所有重要工作

## 系统兼容性

- **Windows**: 使用 `shutdown /s /t 0` 命令
- **macOS**: 使用 `sudo shutdown -h now` 命令 (需要管理员权限)
- **Linux**: 使用 `shutdown -h now` 命令 (需要管理员权限)

## 开发

### 项目结构
```
my-electron-app/
├── index.html              # 主界面
├── electron.main.cjs        # Electron 主进程
├── preload.js              # 预加载脚本
├── shutdown-settings.json  # 配置文件
├── package.json            # 项目配置
└── README.md              # 说明文档
```

### 技术栈
- **Electron**: 跨平台桌面应用框架
- **Bootstrap 5**: UI 框架
- **JavaScript**: 应用逻辑
- **Node.js**: 后端功能

## 许可证

MIT License
